# 变量定义和数据流说明

## 核心变量定义

### 智能体相关变量
- **x_t**: 智能体输出量（与环境直接交互的量）
  - 维度: [batch_size, state_dim]
  - 这是智能体真正与环境交互的输出
  - 在网络中通过 `output_head` 预测

- **y_t**: 智能体内部状态
  - 维度: [batch_size, state_dim]
  - 智能体的内部表示，不直接与环境交互
  - 在网络中通过 `internal_state_head` 预测

- **x_t[sub_x]**: 智能体输出量子集
  - 维度: [batch_size, sub_x_dim]
  - 传递给环境的智能体信息
  - sub_x_dim = state_dim * sub_x_ratio

### 环境相关变量
- **c_t**: 环境直接生成量（环境上下文）
  - 维度: [batch_size, context_dim]
  - 这是你提到的"直接生成的量c"
  - 环境可以直接控制和生成的量

- **a_t**: 环境内部状态
  - 维度: [batch_size, action_dim]
  - 这是你提到的"环境内部状态a"
  - 环境的内部表示，影响环境的行为

- **c_t[sub_c]**: 环境直接生成量子集
  - 维度: [batch_size, sub_c_dim]
  - 这是你提到的"直接影响agent的部分"
  - 传递给智能体的环境信息
  - sub_c_dim = context_dim * sub_c_ratio

## 数据流关系

### 环境网络 (EnvironmentNetwork)
```
输入: (c_t, a_t, x_t[sub_x])
输出: (c_{t+1}, a_{t+1}, physics_score)

数据流:
c_t (直接生成量) + a_t (环境内部状态) + x_t[sub_x] (智能体交互量子集)
    ↓
编码器处理
    ↓
c_{t+1} (下一步直接生成量) + a_{t+1} (下一步环境内部状态)
```

### 智能体网络 (AgentNetwork)
```
输入: (x_t, y_t, c_t[sub_c])
输出: (x_{t+1}, y_{t+1})

数据流:
x_t (智能体输出量) + y_t (智能体内部状态) + c_t[sub_c] (环境影响子集)
    ↓
编码器处理
    ↓
x_{t+1} (下一步输出量) + y_{t+1} (下一步内部状态)
```

## 信息交换机制

### 智能体 → 环境
- 智能体通过 x_t[sub_x] 向环境传递信息
- 这个子集包含智能体输出量的部分维度
- 环境基于这个信息调整自己的状态

### 环境 → 智能体
- 环境通过 c_t[sub_c] 向智能体传递信息
- 这个子集包含环境直接生成量的部分维度
- 智能体基于这个信息调整自己的行为

## 对抗训练逻辑

### 智能体目标
1. 最小化自身损失
2. 保持内部状态稳定 (y_t 的稳定性)
3. 实现输出目标 (如果有 x_target)
4. 保持状态-输出一致性

### 环境目标
1. 最大化智能体损失（对抗性）
2. 保持物理合理性
3. 生成多样化的状态
4. 保持自身状态的连续性

## 修复的关键问题

1. **变量命名一致性**: 明确了 a_t 是环境内部状态，c_t 是直接生成量
2. **残差连接**: 在网络中添加了残差连接以保持状态连续性
3. **稳定性改进**: 增加了多种正则化项防止训练不稳定
4. **信息交换优化**: 改进了子集选择策略，增加训练稳定性
5. **损失函数完善**: 修复了损失计算中的类型错误

## 使用建议

1. 确保 sub_x_ratio 和 sub_c_ratio 设置合理（0.3-0.5之间）
2. 监控训练过程中的各项损失指标
3. 注意环境和智能体的学习率平衡
4. 定期评估系统的整体性能
