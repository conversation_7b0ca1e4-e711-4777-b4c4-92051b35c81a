"""
神经网络定义 - 环境网络E和智能体网络A
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from config import Config

class EnvironmentNetwork(nn.Module):
    """
    环境网络E - 可学习的环境动态模型
    输入: (c_t, a_t, x_t[sub_x])
    输出: (c_{t+1}, a_{t+1})

    注意：
    - c_t: 直接生成量（环境上下文）
    - a_t: 环境内部状态
    - x_t[sub_x]: 智能体与环境直接交互的量的子集
    """
    def __init__(self, config):
        super(EnvironmentNetwork, self).__init__()
        self.config = config

        # 计算输入维度
        sub_x_dim = int(config.state_dim * config.sub_x_ratio)
        input_dim = config.context_dim + config.action_dim + sub_x_dim

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, config.hidden_dim),
            nn.<PERSON><PERSON>(),
            nn.Dropout(0.1),
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.<PERSON>(),
            nn.Dropout(0.1),
            nn.Linear(config.hidden_dim, config.hidden_dim)
        )

        # 直接生成量预测头 (c_t -> c_{t+1})
        self.context_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, config.context_dim)
        )

        # 环境内部状态预测头 (a_t -> a_{t+1})
        self.internal_state_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, config.action_dim),
            nn.Tanh()  # 限制内部状态范围
        )

        # 物理约束层
        self.physics_constraint = nn.Sequential(
            nn.Linear(config.context_dim + config.action_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(config.hidden_dim // 2, 1),
            nn.Sigmoid()  # 输出0-1，表示物理合理性
        )
        
    def forward(self, c_t, a_t, x_t_sub):
        """
        前向传播
        Args:
            c_t: 当前直接生成量（环境上下文） [batch_size, context_dim]
            a_t: 当前环境内部状态 [batch_size, action_dim]
            x_t_sub: 智能体输出量子集（与环境交互的量） [batch_size, sub_x_dim]
        Returns:
            c_next: 下一步直接生成量 [batch_size, context_dim]
            a_next: 下一步环境内部状态 [batch_size, action_dim]
            physics_score: 物理合理性分数 [batch_size, 1]
        """
        # 拼接输入：直接生成量 + 环境内部状态 + 智能体交互量子集
        env_input = torch.cat([c_t, a_t, x_t_sub], dim=-1)

        # 编码
        encoded = self.encoder(env_input)

        # 预测下一步状态
        c_next = self.context_head(encoded)  # 直接生成量更新
        a_next = self.internal_state_head(encoded)  # 环境内部状态更新

        # 添加残差连接以保持状态连续性
        c_next = c_next + c_t * 0.1  # 轻微的残差连接
        a_next = a_next + a_t * 0.1  # 轻微的残差连接

        # 计算物理约束分数
        physics_input = torch.cat([c_next, a_next], dim=-1)
        physics_score = self.physics_constraint(physics_input)

        return c_next, a_next, physics_score
    
    def compute_diversity_loss(self, c_next, a_next, history_buffer):
        """
        计算多样性损失，鼓励环境生成多样化的状态
        Args:
            c_next: 下一步直接生成量
            a_next: 下一步环境内部状态
            history_buffer: 历史状态缓冲区
        """
        if len(history_buffer) == 0:
            return torch.tensor(0.0, device=c_next.device)

        # 计算当前环境状态（直接生成量 + 内部状态）
        current_env_state = torch.cat([c_next, a_next], dim=-1)
        current_state_mean = current_env_state.mean(dim=0)  # 计算批次平均

        # 从deque中获取最近的10个状态（或全部状态如果少于10个）
        recent_states = list(history_buffer)[-10:]  # 转换为list后再切片
        if len(recent_states) == 0:
            return torch.tensor(0.0, device=c_next.device)

        history_states = torch.stack(recent_states)  # 使用最近10个状态

        # 计算当前状态平均与历史状态的距离
        distances = torch.norm(current_state_mean.unsqueeze(0) - history_states, dim=-1)
        diversity_loss = -torch.mean(distances)  # 负号因为我们要最大化多样性

        return diversity_loss


class AgentNetwork(nn.Module):
    """
    智能体网络A - 可学习的智能体策略
    输入: (x_t, y_t, c_t[sub_c])
    输出: (x_{t+1}, y_{t+1})

    注意：
    - x_t: 与环境直接交互的量（智能体输出量）
    - y_t: 智能体内部状态
    - c_t[sub_c]: 环境直接生成量的子集（影响agent的部分）
    """
    def __init__(self, config):
        super(AgentNetwork, self).__init__()
        self.config = config

        # 计算输入维度
        sub_c_dim = int(config.context_dim * config.sub_c_ratio)
        input_dim = config.state_dim + config.state_dim + sub_c_dim  # x_t + y_t + c_t[sub_c]

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(config.hidden_dim, config.hidden_dim)
        )

        # 输出预测头（x是与环境交互的量）
        self.output_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, config.state_dim)
        )

        # 内部状态预测头（y是智能体内部状态）
        self.internal_state_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim),
            nn.ReLU(),
            nn.Linear(config.hidden_dim, config.state_dim)
        )

    def forward(self, x_t, y_t, c_t_sub):
        """
        前向传播
        Args:
            x_t: 当前输出量（与环境交互的量） [batch_size, state_dim]
            y_t: 当前智能体内部状态 [batch_size, state_dim]
            c_t_sub: 环境直接生成量子集（影响agent的部分） [batch_size, sub_c_dim]
        Returns:
            x_next: 下一步输出量（与环境交互的量） [batch_size, state_dim]
            y_next: 下一步智能体内部状态 [batch_size, state_dim]
        """
        # 拼接输入：智能体输出量 + 智能体内部状态 + 环境影响子集
        agent_input = torch.cat([x_t, y_t, c_t_sub], dim=-1)

        # 编码
        encoded = self.encoder(agent_input)

        # 预测下一步
        x_next = self.output_head(encoded)  # 输出量（与环境交互）
        y_next = self.internal_state_head(encoded)   # 智能体内部状态

        # 添加残差连接以保持状态连续性
        x_next = x_next + x_t * 0.1  # 轻微的残差连接
        y_next = y_next + y_t * 0.1  # 轻微的残差连接

        return x_next, y_next


def create_networks(config):
    """创建网络实例"""
    env_net = EnvironmentNetwork(config).to(config.device)
    agent_net = AgentNetwork(config).to(config.device)
    
    return env_net, agent_net
