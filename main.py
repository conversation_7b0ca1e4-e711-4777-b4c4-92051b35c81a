"""
主训练脚本 - 对抗环境-智能体系统的训练入口
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import json
import os
from datetime import datetime
import argparse

from config import Config
from adversarial_trainer import AdversarialTrainer
from evaluation_tasks import run_comprehensive_evaluation

class DataGenerator:
    """数据生成器 - 生成训练用的批次数据"""
    
    def __init__(self, config):
        self.config = config
        self.device = config.device
        
    def generate_batch(self, batch_size=None):
        """
        生成一个训练批次
        Args:
            batch_size: 批次大小，默认使用配置中的值
        Returns:
            batch_data: 批次数据字典
        """
        if batch_size is None:
            batch_size = self.config.batch_size

        # 生成随机初始数据
        x_t = torch.randn(batch_size, self.config.state_dim, device=self.device) * 0.5  # 智能体输出量（与环境交互）
        y_t = torch.randn(batch_size, self.config.state_dim, device=self.device) * 0.1  # 智能体内部状态
        c_t = torch.randn(batch_size, self.config.context_dim, device=self.device) * 0.3  # 环境直接生成量（上下文）
        a_t = torch.randn(batch_size, self.config.action_dim, device=self.device) * 0.2   # 环境内部状态

        return {
            'x_t': x_t,  # 智能体输出量
            'y_t': y_t,  # 智能体内部状态
            'c_t': c_t,  # 环境直接生成量
            'a_t': a_t   # 环境内部状态
        }

def save_checkpoint(trainer, episode, save_dir):
    """保存训练检查点"""
    checkpoint = {
        'episode': episode,
        'env_net_state_dict': trainer.env_net.state_dict(),
        'agent_net_state_dict': trainer.agent_net.state_dict(),
        'env_optimizer_state_dict': trainer.env_optimizer.state_dict(),
        'agent_optimizer_state_dict': trainer.agent_optimizer.state_dict(),
        'config': trainer.config.__dict__
    }
    
    checkpoint_path = os.path.join(save_dir, f'checkpoint_episode_{episode}.pth')
    torch.save(checkpoint, checkpoint_path)
    print(f"检查点已保存: {checkpoint_path}")

def load_checkpoint(checkpoint_path, trainer):
    """加载训练检查点"""
    checkpoint = torch.load(checkpoint_path, map_location=trainer.device)
    
    trainer.env_net.load_state_dict(checkpoint['env_net_state_dict'])
    trainer.agent_net.load_state_dict(checkpoint['agent_net_state_dict'])
    trainer.env_optimizer.load_state_dict(checkpoint['env_optimizer_state_dict'])
    trainer.agent_optimizer.load_state_dict(checkpoint['agent_optimizer_state_dict'])
    
    return checkpoint['episode']

def plot_training_curves(agent_losses, env_losses, save_dir):
    """绘制训练曲线"""
    plt.figure(figsize=(12, 5))
    
    # 智能体损失
    plt.subplot(1, 2, 1)
    plt.plot(agent_losses)
    plt.title('Agent Loss')
    plt.xlabel('Episode')
    plt.ylabel('Loss')
    plt.grid(True)
    
    # 环境损失
    plt.subplot(1, 2, 2)
    plt.plot(env_losses)
    plt.title('Environment Loss')
    plt.xlabel('Episode')
    plt.ylabel('Loss')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'training_curves.png'))
    plt.close()

def main():
    """主训练函数"""
    parser = argparse.ArgumentParser(description='对抗环境-智能体训练')
    parser.add_argument('--resume', type=str, help='恢复训练的检查点路径')
    parser.add_argument('--eval_only', action='store_true', help='仅运行评估')
    parser.add_argument('--save_dir', type=str, default='./results', help='结果保存目录')
    
    args = parser.parse_args()
    
    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_dir = os.path.join(args.save_dir, f'experiment_{timestamp}')
    os.makedirs(save_dir, exist_ok=True)
    
    # 初始化配置和训练器
    config = Config()
    trainer = AdversarialTrainer(config)
    data_generator = DataGenerator(config)
    
    print(f"使用设备: {config.device}")
    print(f"结果保存到: {save_dir}")
    
    # 加载检查点（如果指定）
    start_episode = 0
    if args.resume:
        start_episode = load_checkpoint(args.resume, trainer)
        print(f"从第 {start_episode} 轮恢复训练")
    
    # 如果只是评估模式
    if args.eval_only:
        print("运行评估模式...")
        results = run_comprehensive_evaluation(trainer.agent_net, trainer.env_net, config)
        
        # 保存评估结果
        with open(os.path.join(save_dir, 'evaluation_results.json'), 'w') as f:
            json.dump(results, f, indent=2)
        
        print("评估完成！")
        return
    
    # 训练循环
    print("开始训练...")
    training_metrics = []
    
    for episode in range(start_episode, config.total_episodes):
        trainer.episode_count = episode
        
        # 生成训练数据
        batch_data = data_generator.generate_batch()
        
        # 执行训练步骤
        metrics = trainer.train_step(batch_data)
        training_metrics.append(metrics)
        
        # 记录损失
        trainer.agent_losses.append(metrics['total_agent_loss'])
        trainer.env_losses.append(metrics['total_env_loss'])
        
        # 更新学习率
        trainer.agent_scheduler.step()
        if episode >= config.adversarial_start:
            trainer.env_scheduler.step()
        
        # 打印进度
        if episode % 100 == 0:
            print(f"Episode {episode}/{config.total_episodes}")
            print(f"  Agent Loss: {metrics['total_agent_loss']:.4f}")
            print(f"  Env Loss: {metrics['total_env_loss']:.4f}")
            if episode >= config.adversarial_start:
                print(f"  对抗训练模式激活")
        
        # 定期评估
        if episode % config.eval_frequency == 0 and episode > 0:
            print(f"\n=== 第 {episode} 轮评估 ===")
            eval_results = run_comprehensive_evaluation(trainer.agent_net, trainer.env_net, config)
            
            # 保存评估结果
            eval_file = os.path.join(save_dir, f'eval_episode_{episode}.json')
            with open(eval_file, 'w') as f:
                json.dump(eval_results, f, indent=2)
            
            print(f"综合得分: {eval_results['overall_score']:.3f}")
            print("=" * 50)
        
        # 保存检查点
        if episode % 1000 == 0 and episode > 0:
            save_checkpoint(trainer, episode, save_dir)
        
        # 绘制训练曲线
        if episode % 500 == 0 and episode > 0:
            plot_training_curves(trainer.agent_losses, trainer.env_losses, save_dir)
    
    # 训练完成
    print("训练完成！")
    
    # 最终评估
    print("运行最终评估...")
    final_results = run_comprehensive_evaluation(trainer.agent_net, trainer.env_net, config)
    
    # 保存最终结果
    with open(os.path.join(save_dir, 'final_results.json'), 'w') as f:
        json.dump(final_results, f, indent=2)
    
    # 保存最终模型
    save_checkpoint(trainer, config.total_episodes, save_dir)
    
    # 保存训练指标
    with open(os.path.join(save_dir, 'training_metrics.json'), 'w') as f:
        json.dump(training_metrics, f, indent=2)
    
    # 绘制最终训练曲线
    plot_training_curves(trainer.agent_losses, trainer.env_losses, save_dir)
    
    print(f"所有结果已保存到: {save_dir}")
    print(f"最终综合得分: {final_results['overall_score']:.3f}")

if __name__ == "__main__":
    main()
